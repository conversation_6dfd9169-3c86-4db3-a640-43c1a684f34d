import { DeleteOutlined, PlusOutlined } from '@ant-design/icons'
import { Fieldset } from '@fe/rockrose'
import { <PERSON><PERSON>, Card, Drawer, Flex, Form, Input, Radio, Select, Switch } from 'antd'
import React, { useEffect } from 'react'
import UploadComponent from '../../../../../components/Upload'
import { PARTNER_STATUS_OPTIONS, PARTNER_TYPE_OPTIONS } from '../../../../../consts/partner'
import { email, idCard, phone, required } from '../../../../../consts/rules'
import { IPartnerListItem, IPrBankAccountInfo, prPartnerUploadAPI } from '../../store'

interface PartnerFormProps {
  open: boolean
  partner?: IPartnerListItem
  onCancel: () => void
  onSubmit: (values: IPartnerListItem) => void
}

const PartnerForm: React.FC<PartnerFormProps> = ({ open, partner, onCancel, onSubmit }) => {
  const [form] = Form.useForm()
  const formPartnerType = Form.useWatch('partnerType', form)
  // 处理默认银行卡的互斥设置
  const handleDefaultBankChange = (changedIndex: number, isDefault: boolean) => {
    if (isDefault) {
      // 当前银行卡设置为默认时，将其他银行卡设置为非默认
      const bankInfos = form.getFieldValue('bankInfos') || []
      const updatedBankInfos = bankInfos.map((bank: IPrBankAccountInfo, index: number) => ({
        ...bank,
        isDefault: index === changedIndex,
      }))

      form.setFieldValue('bankInfos', updatedBankInfos)
    }
  }

  useEffect(() => {
    if (open && partner) {
      form.setFieldsValue({
        ...partner,
        serviceContent: partner.serviceContent ? partner.serviceContent.split(',').filter(Boolean) : [],
      })
    } else {
      form.resetFields()
    }
  }, [open, partner, form])

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      const params = {
        ...partner,
        ...values,
        serviceContent: Array.isArray(values.serviceContent)
          ? values.serviceContent.join(',')
          : values.serviceContent || '',
      }

      // 处理银行卡信息
      if (params.bankInfos && params.bankInfos.length > 0) {
        // 清理银行卡信息中的时间字段
        params.bankInfos = params.bankInfos.map((bank: IPrBankAccountInfo) => {
          const cleanBank = { ...bank }
          delete cleanBank.createTime
          delete cleanBank.updateTime
          return cleanBank
        })

        // 确保只有一张银行卡被设为默认
        const defaultCards = params.bankInfos.filter((bank: IPrBankAccountInfo) => bank.isDefault)

        if (defaultCards.length > 1) {
          // 如果有多张默认卡，只保留第一张为默认
          params.bankInfos.forEach((bank: IPrBankAccountInfo, index: number) => {
            bank.isDefault = index === 0 && bank.isDefault
          })
        } else if (defaultCards.length === 0 && params.bankInfos.length === 1) {
          // 如果只有一张卡且没有设为默认，自动设为默认
          params.bankInfos[0].isDefault = true
        }
      }

      delete params.createTime
      delete params.updateTime
      delete params.creator
      delete params.lastModifier

      onSubmit(params)
    } catch (error) {
      console.error('表单验证失败:', error)
    }
  }

  return (
    <Drawer
      title={partner ? '编辑合作商' : '添加合作商'}
      open={open}
      onClose={onCancel}
      width={800}
      destroyOnHidden
      extra={
        <Button type="primary" onClick={handleSubmit}>
          立即保存
        </Button>
      }>
      <Form
        form={form}
        layout="horizontal"
        colon={false}
        preserve={false}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 16 }}>
        <Flex vertical gap={24}>
          <Fieldset title="1、基础信息" direction="vertical" className="no-padding-b">
            <Form.Item name="partnerName" label="合作商名称" rules={[{ required: true, message: '请输入合作商名称' }]}>
              <Input placeholder="请输入合作商名称" />
            </Form.Item>

            <Form.Item name="shortName" label="简称" rules={[required]}>
              <Input placeholder="请输入简称" />
            </Form.Item>

            <Form.Item name="partnerType" label="合作商类型" initialValue={1} rules={[required]}>
              <Radio.Group options={PARTNER_TYPE_OPTIONS} />
            </Form.Item>

            <Form.Item name="serviceContent" label="服务内容" rules={[required]}>
              <Select
                mode="tags"
                placeholder="请输入服务内容，如：摄影、收音等"
                style={{ width: '100%' }}
                tokenSeparators={[',']}
                options={[
                  { label: '摄影', value: '摄影' },
                  { label: '收音', value: '收音' },
                  { label: '灯光', value: '灯光' },
                  { label: '后期', value: '后期' },
                  { label: '美术', value: '美术' },
                  { label: '服化', value: '服化' },
                  { label: '演员', value: '演员' },
                ]}
              />
            </Form.Item>

            <Form.Item name="status" label="合作状态" initialValue={1} rules={[required]}>
              <Radio.Group options={PARTNER_STATUS_OPTIONS} />
            </Form.Item>
          </Fieldset>

          <Fieldset
            title={formPartnerType === 2 ? '2、个人信息' : '2、法定代表人'}
            direction="vertical"
            className="no-padding-b">
            <Form.Item
              name="legalRepresentative"
              label={formPartnerType === 2 ? '姓名' : '法定代表人'}
              rules={[required]}>
              <Input placeholder={formPartnerType === 2 ? '请输入姓名' : '请输入法定代表人姓名'} />
            </Form.Item>

            <Form.Item
              name="legalPhone"
              label={formPartnerType === 2 ? '手机号码' : '法人电话'}
              rules={[required, phone]}>
              <Input type="tel" placeholder={formPartnerType === 2 ? '请输入手机号码' : '请输入法人联系电话'} />
            </Form.Item>

            <Form.Item
              name="legalIdNumber"
              label={formPartnerType === 2 ? '身份证号' : '法人证件号'}
              rules={[required, idCard]}>
              <Input placeholder={formPartnerType === 2 ? '请输入身份证号码' : '请输入法人身份证号码'} />
            </Form.Item>

            <Form.Item name="legalIdFrontPath" label="身份证正面" rules={[required]}>
              <UploadComponent action={prPartnerUploadAPI} type="image" accept=".png,.jpg,.jpeg" multiple={false} />
            </Form.Item>

            <Form.Item name="legalIdBackPath" label="身份证背面" rules={[required]}>
              <UploadComponent action={prPartnerUploadAPI} type="image" accept=".png,.jpg,.jpeg" multiple={false} />
            </Form.Item>
          </Fieldset>

          <Fieldset title="3、联系信息" direction="vertical" className="no-padding-b">
            <Form.Item name="contactPerson" label="日常联系人" rules={[required]}>
              <Input placeholder="请输入日常联系人姓名" />
            </Form.Item>

            <Form.Item name="contactPhone" label="联系电话" rules={[required, phone]}>
              <Input type="tel" placeholder="请输入联系电话" />
            </Form.Item>

            <Form.Item name="contactEmail" label="联系邮箱" rules={[required, email]}>
              <Input type="email" placeholder="请输入联系邮箱" />
            </Form.Item>
          </Fieldset>

          <Fieldset title="4、地址信息" direction="vertical" className="no-padding-b">
            <Form.Item name="address" label={formPartnerType === 2 ? '户籍地址' : '注册地址'} rules={[required]}>
              <Input.TextArea placeholder={formPartnerType === 2 ? '请输入户籍地址' : '请输入注册地址'} rows={2} />
            </Form.Item>

            <Form.Item
              name="actualAddress"
              label={formPartnerType === 2 ? '现居住地址' : '实际经营地址'}
              rules={[required]}>
              <Input.TextArea
                placeholder={formPartnerType === 2 ? '请输入现居住地址' : '请输入实际经营地址'}
                rows={2}
              />
            </Form.Item>
          </Fieldset>

          {formPartnerType === 1 && (
            <Fieldset title="5、营业执照" direction="vertical" className="no-padding-b">
              <Form.Item name="businessLicense" label="营业执照号" rules={[required]}>
                <Input placeholder="请输入营业执照号（统一社会信用代码）" />
              </Form.Item>

              <Form.Item name="businessLicensePath" label="营业执照" rules={[required]}>
                <UploadComponent action={prPartnerUploadAPI} type="image" accept=".png,.jpg,.jpeg" multiple={false} />
              </Form.Item>
            </Fieldset>
          )}

          <Fieldset title={formPartnerType === 2 ? '5、银行账户' : '6、银行账户'} direction="vertical">
            <Form.List name="bankInfos">
              {(fields, { add, remove }) => (
                <>
                  {fields.map(({ key, name, ...restField }) => (
                    <Card
                      key={key}
                      size="small"
                      title={`银行卡 ${name + 1}`}
                      extra={
                        fields.length > 1 ? (
                          <Button
                            type="link"
                            danger
                            icon={<DeleteOutlined />}
                            onClick={() => remove(name)}
                            size="small">
                            删除
                          </Button>
                        ) : null
                      }>
                      <Form.Item
                        {...restField}
                        name={[name, 'bankName']}
                        label="开户行"
                        rules={[{ required: true, message: '请输入开户行名称' }]}>
                        <Input placeholder="请输入开户行名称" />
                      </Form.Item>

                      <Form.Item
                        {...restField}
                        name={[name, 'accountName']}
                        label="账户名称"
                        rules={[{ required: true, message: '请输入账户名称' }]}>
                        <Input placeholder="请输入账户名称" />
                      </Form.Item>

                      <Form.Item
                        {...restField}
                        name={[name, 'accountNumber']}
                        label="卡号"
                        rules={[{ required: true, message: '请输入银行卡号' }]}>
                        <Input placeholder="请输入银行卡号" />
                      </Form.Item>

                      <Form.Item {...restField} name={[name, 'isDefault']} label="设为默认" valuePropName="checked">
                        <Switch onChange={checked => handleDefaultBankChange(name, checked)} />
                      </Form.Item>
                    </Card>
                  ))}
                  <Button color="primary" variant="filled" onClick={() => add()} icon={<PlusOutlined />}>
                    添加银行卡
                  </Button>
                </>
              )}
            </Form.List>
          </Fieldset>

          <Fieldset title={formPartnerType === 2 ? '6、其他' : '7、其他'} direction="vertical" className="no-padding-b">
            <Form.Item name="remark" label="备注">
              <Input.TextArea placeholder="请输入备注信息" rows={3} />
            </Form.Item>
          </Fieldset>
        </Flex>
      </Form>
    </Drawer>
  )
}

export default PartnerForm
