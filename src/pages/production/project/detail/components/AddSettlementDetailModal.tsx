import {
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  Switch,
  message,
  Typography
} from 'antd'
import React, { useState } from 'react'
import { IPrProductionSettlementDetail } from '../store'
import useProjectDetailStore from '../store'
import Upload from '../../../../../components/Upload'

const { Text } = Typography
const { TextArea } = Input

interface IAddSettlementDetailModalProps {
  open: boolean
  onCancel: () => void
  onSuccess: () => void
  itemId: number
  maxAmount?: number
}

const AddSettlementDetailModal: React.FC<IAddSettlementDetailModalProps> = ({
  open,
  onCancel,
  onSuccess,
  itemId,
  maxAmount = 0,
}) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [invoiceFiles, setInvoiceFiles] = useState<string[]>([])

  const { addSettlementDetail } = useProjectDetailStore()

  // 处理文件上传
  const handleUploadChange = (files: string | string[]) => {
    const fileArray = Array.isArray(files) ? files : [files].filter(Boolean)
    setInvoiceFiles(fileArray)
  }

  // 处理提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()

      setLoading(true)

      const submitData: IPrProductionSettlementDetail = {
        itemId,
        amount: values.amount,
        status: 0, // 默认待审核
        invoicePathList: invoiceFiles,
        paymentMethod: values.paymentMethod,
        description: values.description,
        isApprovalInitiated: values.isApprovalInitiated || false,
      }

      const success = await addSettlementDetail(submitData)

      if (success) {
        message.success('添加成功')
        form.resetFields()
        setInvoiceFiles([])
        onSuccess()
      } else {
        message.error('添加失败')
      }
    } catch (error) {
      console.error('添加支付明细失败:', error)
      message.error('添加失败')
    } finally {
      setLoading(false)
    }
  }

  // 处理取消
  const handleCancel = () => {
    form.resetFields()
    setInvoiceFiles([])
    onCancel()
  }

  return (
    <Modal
      title="添加支付明细"
      open={open}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={loading}
      width={600}
      destroyOnHidden>
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          paymentMethod: '1',
          isApprovalInitiated: false,
        }}>
        <Form.Item
          label="支付金额"
          name="amount"
          rules={[
            { required: true, message: '请输入支付金额' },
            {
              validator: (_, value) => {
                if (!value || value <= 0) {
                  return Promise.reject(new Error('金额必须大于0'))
                }
                if (maxAmount > 0 && value > maxAmount) {
                  return Promise.reject(new Error(`金额不能超过待支付金额 ¥${maxAmount.toLocaleString()}`))
                }
                return Promise.resolve()
              }
            }
          ]}>
          <InputNumber
            style={{ width: '100%' }}
            placeholder="请输入支付金额"
            precision={2}
            min={0}
            max={maxAmount > 0 ? maxAmount : undefined}
            
          />
        </Form.Item>

        {maxAmount > 0 && (
          <div style={{ marginBottom: 16 }}>
            <Text type="secondary">
              待支付金额: ¥{maxAmount.toLocaleString()}
            </Text>
          </div>
        )}

        <Form.Item
          label="支付方式"
          name="paymentMethod"
          rules={[{ required: true, message: '请选择支付方式' }]}>
          <Select placeholder="请选择支付方式">
            <Select.Option value="1">转账</Select.Option>
            <Select.Option value="2">平台</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="票据上传"
          name="invoiceFiles">
          <Upload
            value={invoiceFiles}
            onChange={handleUploadChange}
            action="/File/Upload"
            type="image"
            accept="image/*,.pdf"
            multiple
            maxCount={8}
          />
          <Text type="secondary" style={{ fontSize: 12 }}>
            支持图片和PDF格式，最多上传8个文件
          </Text>
        </Form.Item>

        <Form.Item
          label="备注说明"
          name="description">
          <TextArea
            placeholder="请输入备注说明"
            rows={3}
            maxLength={500}
            showCount
          />
        </Form.Item>

        <Form.Item
          label="发起审批"
          name="isApprovalInitiated"
          valuePropName="checked">
          <Switch />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default AddSettlementDetailModal
