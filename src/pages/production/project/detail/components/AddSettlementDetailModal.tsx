import {
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  Switch,
  Upload,
  message,
  Space,
  Typography
} from 'antd'
import { PlusOutlined } from '@ant-design/icons'
import React, { useState } from 'react'
import { IPrProductionSettlementDetail } from '../store'
import useProjectDetailStore from '../store'
import { getUploadProps } from '../../../../../utils/file'
import type { UploadFile, UploadProps } from 'antd'

const { Text } = Typography
const { TextArea } = Input

interface IAddSettlementDetailModalProps {
  open: boolean
  onCancel: () => void
  onSuccess: () => void
  itemId: number
  maxAmount?: number
}

const AddSettlementDetailModal: React.FC<IAddSettlementDetailModalProps> = ({
  open,
  onCancel,
  onSuccess,
  itemId,
  maxAmount = 0,
}) => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [fileList, setFileList] = useState<UploadFile[]>([])

  const { addSettlementDetail } = useProjectDetailStore()

  // 处理文件上传
  const handleUploadChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    setFileList(newFileList)
  }

  // 处理提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      
      setLoading(true)

      // 处理上传的文件路径
      const invoicePathList = fileList
        .filter(file => file.status === 'done' && file.response?.data?.data?.[0]?.url)
        .map(file => file.response.data.data[0].url)

      const submitData: IPrProductionSettlementDetail = {
        itemId,
        amount: values.amount,
        status: 0, // 默认待审核
        invoicePathList,
        paymentMethod: values.paymentMethod,
        description: values.description,
        isApprovalInitiated: values.isApprovalInitiated || false,
      }

      const success = await addSettlementDetail(submitData)
      
      if (success) {
        message.success('添加成功')
        form.resetFields()
        setFileList([])
        onSuccess()
      } else {
        message.error('添加失败')
      }
    } catch (error) {
      console.error('添加支付明细失败:', error)
      message.error('添加失败')
    } finally {
      setLoading(false)
    }
  }

  // 处理取消
  const handleCancel = () => {
    form.resetFields()
    setFileList([])
    onCancel()
  }

  return (
    <Modal
      title="添加支付明细"
      open={open}
      onOk={handleSubmit}
      onCancel={handleCancel}
      confirmLoading={loading}
      width={600}
      destroyOnClose>
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          paymentMethod: '1',
          isApprovalInitiated: false,
        }}>
        <Form.Item
          label="支付金额"
          name="amount"
          rules={[
            { required: true, message: '请输入支付金额' },
            { type: 'number', min: 0.01, message: '金额必须大于0' },
            ...(maxAmount > 0 ? [
              { type: 'number', max: maxAmount, message: `金额不能超过待支付金额 ¥${maxAmount.toLocaleString()}` }
            ] : [])
          ]}>
          <InputNumber
            style={{ width: '100%' }}
            placeholder="请输入支付金额"
            precision={2}
            min={0}
            max={maxAmount > 0 ? maxAmount : undefined}
            formatter={value => `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
            parser={value => value!.replace(/¥\s?|(,*)/g, '')}
          />
        </Form.Item>

        {maxAmount > 0 && (
          <div style={{ marginBottom: 16 }}>
            <Text type="secondary">
              待支付金额: ¥{maxAmount.toLocaleString()}
            </Text>
          </div>
        )}

        <Form.Item
          label="支付方式"
          name="paymentMethod"
          rules={[{ required: true, message: '请选择支付方式' }]}>
          <Select placeholder="请选择支付方式">
            <Select.Option value="1">转账</Select.Option>
            <Select.Option value="2">平台</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="票据上传"
          name="invoiceFiles">
          <Upload
            {...getUploadProps('/File/Upload')}
            listType="picture-card"
            fileList={fileList}
            onChange={handleUploadChange}
            accept="image/*,.pdf"
            multiple
            name="filename">
            {fileList.length >= 8 ? null : (
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>上传票据</div>
              </div>
            )}
          </Upload>
          <Text type="secondary" style={{ fontSize: 12 }}>
            支持图片和PDF格式，最多上传8个文件
          </Text>
        </Form.Item>

        <Form.Item
          label="备注说明"
          name="description">
          <TextArea
            placeholder="请输入备注说明"
            rows={3}
            maxLength={500}
            showCount
          />
        </Form.Item>

        <Form.Item
          label="发起审批"
          name="isApprovalInitiated"
          valuePropName="checked">
          <Switch />
        </Form.Item>
      </Form>
    </Modal>
  )
}

export default AddSettlementDetailModal
