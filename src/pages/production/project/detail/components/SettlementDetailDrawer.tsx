import { Drawer, Tabs, Spin, message, Space, Card, Tag, Typography, Descriptions } from 'antd'
import React, { useEffect, useState } from 'react'
import useProjectDetailStore, { IPrProductionSettlementItem } from '../store'
import SettlementContractDetail from './SettlementContractDetail'
import SettlementPaymentDetail from './SettlementPaymentDetail'
import { FileTextOutlined, EyeOutlined } from '@ant-design/icons'
import { getBudgetCategoryLabel, getBudgetSubcategoryLabel } from '../../../../../consts/budget'
import styles from './SettlementDetailDrawer.scss'

const { Text } = Typography

interface ISettlementDetailDrawerProps {
  open: boolean
  onClose: () => void
  itemId: number | null
  productionId: number
}

const SettlementDetailDrawer: React.FC<ISettlementDetailDrawerProps> = ({
  open,
  onClose,
  itemId,
  productionId,
}) => {
  const [loading, setLoading] = useState(false)
  const [settlementItem, setSettlementItem] = useState<IPrProductionSettlementItem | null>(null)
  const [refreshKey, setRefreshKey] = useState(0)

  const { getSettlementItemsById } = useProjectDetailStore()

  // 加载结算项目详情
  const loadSettlementDetail = async () => {
    if (!itemId) return

    setLoading(true)
    try {
      const item = await getSettlementItemsById(itemId)
      setSettlementItem(item)
    } catch (error) {
      console.error('加载结算项目详情失败:', error)
      message.error('加载结算项目详情失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (open && itemId) {
      loadSettlementDetail()
    }
  }, [open, itemId, refreshKey])

  // 处理支付明细更新后的刷新
  const handlePaymentDetailRefresh = () => {
    setRefreshKey(prev => prev + 1)
  }

  // 关闭抽屉
  const handleClose = () => {
    setSettlementItem(null)
    onClose()
  }

  const tabItems = [
    {
      key: 'contract',
      label: '合同详情',
      children: (
        <SettlementContractDetail
          settlementItem={settlementItem}
          loading={loading}
        />
      ),
    },
    {
      key: 'payment',
      label: '支付明细',
      children: (
        <SettlementPaymentDetail
          settlementItem={settlementItem}
          loading={loading}
          onRefresh={handlePaymentDetailRefresh}
          productionId={productionId}
        />
      ),
    },
  ]

  return (
    <Drawer
      title={`结算详情 - ${settlementItem?.itemName || '加载中...'}`}
      placement="right"
      width="60%"
      open={open}
      onClose={handleClose}
      className={styles.drawer}
      destroyOnHidden>
      <Spin spinning={loading}>
        <Space direction="vertical" size={16} style={{ width: '100%' }}>
          {/* 结算项目基本信息 */}
          {settlementItem ? <Card
            title={
              <Space>
                <FileTextOutlined />
                <Text strong>结算项目信息</Text>
              </Space>
            }
            size="small">
            <Descriptions column={2} size="small" colon={false}>
              <Descriptions.Item label="项目名称">
                <Space>
                  {settlementItem.itemName}
                  {settlementItem.isPackage ? (
                    <Tag color="volcano">打包</Tag>
                  ) : (
                    <Tag color="cyan">单项</Tag>
                  )}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="总价">
                <Text strong type="danger">
                  ¥{(settlementItem.totalPrice || 0).toLocaleString()}
                </Text>
              </Descriptions.Item>
              {settlementItem.quotedPrice && (
                <Descriptions.Item label="单价">
                  ¥{settlementItem.quotedPrice.toLocaleString()}
                </Descriptions.Item>
              )}
              {settlementItem.dayCount && (
                <Descriptions.Item label="天数">
                  {settlementItem.dayCount}
                </Descriptions.Item>
              )}
              {settlementItem.rate && (
                <Descriptions.Item label="费率">
                  {settlementItem.rate}%
                </Descriptions.Item>
              )}
              <Descriptions.Item label="发票">
                {settlementItem.hasInvoice ? '有' : '无'}
              </Descriptions.Item>
              {settlementItem.description && (
                <Descriptions.Item label="备注" span={2}>
                  {settlementItem.description}
                </Descriptions.Item>
              )}
            </Descriptions>
          </Card>
            : null}
          {/* 项目详情（如果是打包项目） */}
          {settlementItem && settlementItem.isPackage && settlementItem.itemsDetail && settlementItem.itemsDetail.length > 0 && (
            <Card
              title={
                <Space>
                  <FileTextOutlined />
                  <Text strong>包含项目详情</Text>
                </Space>
              }
              size="small">
              <Space direction="vertical" size={12} style={{ width: '100%' }}>
                {settlementItem.itemsDetail.map((detail, index) => (
                  <Card key={index} type="inner" size="small">
                    <Space wrap>
                      <Tag color="geekblue">
                        {getBudgetCategoryLabel(detail.category)}
                      </Tag>
                      <Text strong>
                        {detail.subcategoryLabel || getBudgetSubcategoryLabel(detail.subcategory)}
                      </Text>
                      {detail.personCount > 0 && (
                        <Tag color="orange">
                          {detail.personCount}人
                        </Tag>
                      )}
                    </Space>
                  </Card>
                ))}
              </Space>
            </Card>
          )}
          <Tabs
            defaultActiveKey="contract"
            items={tabItems}
            size="small"
            indicator={{ size: 32 }}
          />
          </Space>
      </Spin>
    </Drawer>
  )
}

export default SettlementDetailDrawer
