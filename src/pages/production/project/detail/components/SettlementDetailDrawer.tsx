import { Drawer, Tabs, Spin, message } from 'antd'
import React, { useEffect, useState } from 'react'
import useProjectDetailStore, { IPrProductionSettlementItem } from '../store'
import SettlementContractDetail from './SettlementContractDetail'
import SettlementPaymentDetail from './SettlementPaymentDetail'
import styles from './SettlementDetailDrawer.scss'

interface ISettlementDetailDrawerProps {
  open: boolean
  onClose: () => void
  itemId: number | null
  productionId: number
}

const SettlementDetailDrawer: React.FC<ISettlementDetailDrawerProps> = ({
  open,
  onClose,
  itemId,
  productionId,
}) => {
  const [loading, setLoading] = useState(false)
  const [settlementItem, setSettlementItem] = useState<IPrProductionSettlementItem | null>(null)
  const [refreshKey, setRefreshKey] = useState(0)

  const { getSettlementItemsById } = useProjectDetailStore()

  // 加载结算项目详情
  const loadSettlementDetail = async () => {
    if (!itemId) return

    setLoading(true)
    try {
      const item = await getSettlementItemsById(itemId)
      setSettlementItem(item)
    } catch (error) {
      console.error('加载结算项目详情失败:', error)
      message.error('加载结算项目详情失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (open && itemId) {
      loadSettlementDetail()
    }
  }, [open, itemId, refreshKey])

  // 处理支付明细更新后的刷新
  const handlePaymentDetailRefresh = () => {
    setRefreshKey(prev => prev + 1)
  }

  // 关闭抽屉
  const handleClose = () => {
    setSettlementItem(null)
    onClose()
  }

  const tabItems = [
    {
      key: 'contract',
      label: '合同详情',
      children: (
        <SettlementContractDetail
          settlementItem={settlementItem}
          loading={loading}
        />
      ),
    },
    {
      key: 'payment',
      label: '支付明细',
      children: (
        <SettlementPaymentDetail
          settlementItem={settlementItem}
          loading={loading}
          onRefresh={handlePaymentDetailRefresh}
          productionId={productionId}
        />
      ),
    },
  ]

  return (
    <Drawer
      title={`结算详情 - ${settlementItem?.itemName || '加载中...'}`}
      placement="right"
      width="60%"
      open={open}
      onClose={handleClose}
      className={styles.drawer}
      destroyOnHidden>
      <Spin spinning={loading}>
        <Tabs
          defaultActiveKey="contract"
          items={tabItems}
          size="small"
          indicator={{ size: 32 }}
        />
      </Spin>
    </Drawer>
  )
}

export default SettlementDetailDrawer
