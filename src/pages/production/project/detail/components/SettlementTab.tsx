import { DownloadOutlined, FolderAddOutlined, PlusOutlined, FileTextOutlined, MoreOutlined } from '@ant-design/icons'
import { Dict, ListHeader } from '@fe/rockrose'
import { Button, Card, Descriptions, Empty, Flex, List, message, Popconfirm, Space, Tag, Typography, Badge, Dropdown, Divider } from 'antd'
import React, { useEffect, useMemo, useState } from 'react'
import { getBudgetSubcategoryLabel, getBudgetCategoryLabel } from '../../../../../consts/budget'
import { IProductionListItem } from '../../list/store'
import useProjectDetailStore, { IPrProductionSettlementItem } from '../store'
import { exportBudgetById } from '../../../../../utils/export'
import AddNewBudgetExpenses from './AddBudgetExpenses'
import AddNewBudgetPackage from './AddBudgetPackage'
import EditBudgetItem from './EditBudgetItem'
import SettlementDetailDrawer from './SettlementDetailDrawer'

const { Text } = Typography

interface ISettlementTabProps {
  productionId: number
  project?: IProductionListItem
}

const SettlementTab: React.FC<ISettlementTabProps> = ({ productionId, project }) => {
  const [settlementItems, setSettlementItems] = useState<IPrProductionSettlementItem[]>([])
  const [loading, setLoading] = useState(false)
  const [refreshKey, setRefreshKey] = useState(0)
  const [isPackageModalOpen, setIsPackageModalOpen] = useState(false)
  const [isExpensesModalOpen, setIsExpensesModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [editingItem, setEditingItem] = useState<IPrProductionSettlementItem | undefined>()

  const { getSettlementItems, deleteBudgetItems } = useProjectDetailStore()

  useEffect(() => {
    if (productionId) {
      loadSettlementItems()
    }
  }, [productionId, refreshKey])

  // 加载结算项目列表
  const loadSettlementItems = async () => {
    setLoading(true)
    try {
      const items = await getSettlementItems({ productionId })
      setSettlementItems(items || [])
    } catch (error) {
      console.error('加载结算项目失败:', error)
      message.error('加载结算项目失败')
    } finally {
      setLoading(false)
    }
  }

  // 计算统计信息
  const statistics = useMemo(() => {
    const totalSettlement = settlementItems.reduce((sum, item) => sum + (item.totalPrice || 0), 0)

    // 计算含税总计，使用每个项目的 rate 字段
    const taxTotal = settlementItems.reduce((sum, item) => {
      const itemTotal = item.totalPrice || 0
      const rate = item.rate || 0
      return sum + itemTotal * (1 + rate / 100)
    }, 0)

    return {
      totalSettlement,
      taxTotal,
    }
  }, [settlementItems])

  // 计算已存在的子分类，用于防重复
  const existingSubcategories = useMemo(() => {
    const subcategories = new Set<number>()
    settlementItems.forEach(item => {
      item.itemsDetail?.forEach(detail => {
        if (detail.subcategory !== undefined) {
          subcategories.add(detail.subcategory)
        }
      })
    })
    return subcategories
  }, [settlementItems])

  // 处理添加打包项
  const handleAddPackage = () => {
    setIsPackageModalOpen(true)
  }

  // 处理添加费用项
  const handleAddExpense = () => {
    setIsExpensesModalOpen(true)
  }

  // 处理添加成功后的刷新
  const handleAddSuccess = () => {
    setRefreshKey(prev => prev + 1)
    setEditingItem(undefined)
  }

  // 处理编辑结算项
  const handleEdit = (record: IPrProductionSettlementItem) => {
    setEditingItem(record)
    // 根据是否为打包项决定使用哪个编辑组件
    if (record.isPackage) {
      setIsPackageModalOpen(true)
    } else {
      setIsEditModalOpen(true)
    }
  }

  // 处理删除结算项
  const handleDelete = async (record: IPrProductionSettlementItem) => {
    if (!record?.id) {
      message.error('无法删除该项目')
      return
    }

    try {
      const success = await deleteBudgetItems(record.id)
      if (success) {
        message.success('删除成功')
        setRefreshKey(prev => prev + 1)
      } else {
        message.error('删除失败')
      }
    } catch (error) {
      console.error('删除结算项失败:', error)
      message.error('删除失败')
    }
  }
  // 详情侧边栏状态
  const [detailDrawerOpen, setDetailDrawerOpen] = useState(false)
  const [selectedItemId, setSelectedItemId] = useState<number | null>(null)

  const handleItemClick = (id: number | undefined, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (!id) {
      return
    }
    setSelectedItemId(id)
    setDetailDrawerOpen(true)
  }
  // 处理导出
  const handleExport = async () => {
    try {
      await exportBudgetById(productionId, project?.productionName || '结算表')
      message.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败')
    }
  }

  // 渲染结算项目的子项详情
  const renderItemDetails = (item: IPrProductionSettlementItem) => {
    if (!item.itemsDetail || item.itemsDetail.length === 0) {
      return null
    }

    // 按一级类目分组
    const groupedByCategory = item.itemsDetail.reduce((acc, detail) => {
      const category = detail.category
      if (!acc[category]) {
        acc[category] = []
      }
      acc[category].push(detail)
      return acc
    }, {} as Record<number, typeof item.itemsDetail>)

    return (
      <Card
        size="small"
        type="inner"
        title={
          <Space>
            <FileTextOutlined />
            <Text strong>包含项目</Text>
          </Space>
        }
      >
        <Space direction="horizontal" size="middle" style={{ width: '100%' }} wrap>
          {Object.entries(groupedByCategory).map(([categoryId, details]) => {
            const category = Number(categoryId)

            return (
              <Card
                key={categoryId}
                type="inner"
                title={
                  <Space>
                    <Tag >
                      {getBudgetCategoryLabel(category)}
                    </Tag>
                    <Text type="secondary">
                      {details.length} 项
                    </Text>
                  </Space>
                }
              >
                <Space wrap size="small">
                  {details.map((detail, index) => (
                    <Badge
                      key={index}
                      count={detail.personCount || 0}
                      size="small"
                      showZero={false}
                      offset={[-6, -2]}
                      color="orange"
                    >
                      <Tag >
                        {detail.subcategoryLabel || (detail.subcategory ? getBudgetSubcategoryLabel(detail.subcategory) : '')}
                      </Tag>
                    </Badge>
                  ))}
                </Space>
              </Card>
            )
          })}
        </Space>
      </Card>
    )
  }

  // 渲染单个结算项目
  const renderSettlementItem = (item: IPrProductionSettlementItem) => {

    const actionItems: Array<any> = []
    actionItems.push({
      key: 'edit',
      label: '编辑',
      onClick: () => handleEdit(item),
    })
    // 只有在不是只读模式时才显示编辑/删除按钮
    if (!project?.feNoEdit) {
      actionItems.push({
        key: 'delete',
        label: (
          <Popconfirm
            key="delete"
            title="确认删除"
            description="删除后无法恢复，确认删除该结算项吗？"
            onConfirm={() => handleDelete(item)}
            okText="确认"
            cancelText="取消"
            placement="topRight"
          >
            <Typography.Text type="danger" style={{ width: '100%', display: 'inline-block' }}>
              删除
            </Typography.Text>
          </Popconfirm>
        ),
      })
    }
    return (
      <List.Item key={item.id}>
        <Card size="small" className="full-h hover-move">
          <Flex justify="space-between">
            <Flex vertical
              onClick={e => handleItemClick(item?.id, e)}
              style={{flexGrow:1}}
            >
              <Space align="center">
                {item.isPackage ? (
                  <>
                    <Text strong style={{ fontSize: 16 }}>
                      {item.itemName}
                    </Text>
                    <Tag color="volcano" style={{ borderRadius: 10 }}>
                      打包
                    </Tag>
                  </>
                ) : (
                  <>
                    {/* 显示一级类目 */}
                    {item.itemsDetail?.[0]?.category && (
                      <Tag
                        color="geekblue"
                        style={{ borderRadius: 6, fontSize: 11 }}
                      >
                        {getBudgetCategoryLabel(item.itemsDetail[0].category)}
                      </Tag>
                    )}
                    <Text strong style={{ fontSize: 16 }}>
                      {item.itemsDetail?.[0]?.subcategoryLabel ||
                        (item.itemsDetail?.[0]?.subcategory ? getBudgetSubcategoryLabel(item.itemsDetail[0].subcategory) : '') ||
                        '结算项目'}
                    </Text>
                    <Tag color="cyan" style={{ borderRadius: 10 }}>
                      单项
                    </Tag>
                  </>
                )}
              </Space>
              <Space
                size={0}
                split={<Divider type="vertical" />}
              >

                {!!item.dayCount && (
                  <Dict title="天数" value={item.dayCount || 0} />
                )}
                {!!item.quotedPrice && (
                  <Dict title="单价" value={(project?.currencySymbol || '¥') + item?.quotedPrice?.toLocaleString()} />
                )}
                {!!item.rate && (
                  <Dict title="费率" value={item.rate + '%'} />
                )}
                <Dict title="发票" value={item.hasInvoice ? '有' : '无'} />
                <Dict title="总价" value={<Text strong type="danger" style={{ fontSize: 16 }}>
                  {project?.currencySymbol || '¥'}{(item.totalPrice || 0).toLocaleString()}
                </Text>} />
              </Space>
              {!!item.description && <Dict title="备注" value={item.description || ''} />}
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                {/* 渲染子项详情 */}
                {item.isPackage && renderItemDetails(item)}
              </Space>
            </Flex>

            <Space>
              {actionItems.length > 0 && (
                <Dropdown menu={{ items: actionItems }} trigger={['click']} placement="bottomRight">
                  <Button type="text" icon={<MoreOutlined />} />
                </Dropdown>
              )}
            </Space>
          </Flex>
        </Card>

      </List.Item >

    )
  }

  // 渲染结算列表
  const renderSettlementList = () => {
    if (settlementItems.length === 0) {
      return (
        <Empty
          description="暂无结算数据"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          style={{ marginTop: 60 }}
        />
      )
    }

    return (
      <Space direction="vertical" size={24} style={{ width: '100%' }}>
        <List
          dataSource={settlementItems}
          renderItem={renderSettlementItem}
          size="large"
          style={{
            width: '100%',
            backgroundColor: 'transparent'
          }}
          split={false}
        />

        {/* 统计信息卡片 */}
        <Card size="small" style={{ marginTop: 24 }}>
          <Descriptions
            title="结算统计"
            column={2}
            size="small"
            items={[
              {
                key: 'total',
                label: '结算总计',
                children: (
                  <Text type="danger" strong>
                    {`${project?.currencySymbol || '¥'}${statistics.totalSettlement.toLocaleString()}`}
                  </Text>
                ),
              },
              {
                key: 'tax',
                label: '含税总计',
                children: (
                  <Text type="danger" strong className="fs-lg">
                    {`${project?.currencySymbol || '¥'}${statistics.taxTotal.toLocaleString()}`}
                  </Text>
                ),
              },
            ]}
          />
        </Card>
      </Space>
    )
  }

  return (
    <Flex vertical>
      <ListHeader
        title={
          <Space>
            <span>结算总计</span>
            <Text type="danger" strong className="fs-lg">
              {`${project?.currencySymbol || '¥'}`}
              {statistics.totalSettlement.toLocaleString()}
            </Text>
          </Space>
        }>
        <Space>
          <Button
            color="primary"
            variant="filled"
            shape="round"
            icon={<FolderAddOutlined />}
            onClick={handleAddPackage}>
            添加打包
          </Button>
          <Button
            type="primary"
            ghost
            shape="round"
            icon={<PlusOutlined />}
            onClick={handleAddExpense}>
            添加费用
          </Button>
          <Button
            shape="round"
            icon={<DownloadOutlined />}
            onClick={handleExport}>
            导出结算表
          </Button>
        </Space>
      </ListHeader>

      <div style={{ height: 'calc(100% - 60px)', overflow: 'auto', paddingTop: '30px' }}>
        {loading ? (
          <List loading={true} dataSource={[]} renderItem={() => null} />
        ) : (
          renderSettlementList()
        )}
      </div>

      {/* 添加打包项弹窗 */}
      {isPackageModalOpen ? <AddNewBudgetPackage
        open={isPackageModalOpen}
        onCancel={() => {
          setIsPackageModalOpen(false)
          setEditingItem(undefined)
        }}
        onSuccess={handleAddSuccess}
        productionId={productionId}
        project={project}
        editingItem={editingItem}
        existingSubcategories={existingSubcategories}
        parentType={1} // 结算类型
      /> : null}

      {/* 添加费用项弹窗 */}
      {isExpensesModalOpen ? <AddNewBudgetExpenses
        open={isExpensesModalOpen}
        onCancel={() => {
          setIsExpensesModalOpen(false)
          setEditingItem(undefined)
        }}
        onSuccess={handleAddSuccess}
        productionId={productionId}
        project={project}
        existingSubcategories={existingSubcategories}
        parentType={1} // 结算类型
      /> : null}

      {/* 编辑项目弹窗 */}
      {isEditModalOpen ? <EditBudgetItem
        open={isEditModalOpen}
        onCancel={() => {
          setIsEditModalOpen(false)
          setEditingItem(undefined)
        }}
        onSuccess={handleAddSuccess}
        productionId={productionId}
        project={project}
        editingItem={editingItem}
        parentType={1} // 结算类型
      /> : null}

      {/* 结算详情侧边栏 */}
      {detailDrawerOpen && selectedItemId && (
        <SettlementDetailDrawer
          open={detailDrawerOpen}
          onClose={() => {
            setDetailDrawerOpen(false)
            setSelectedItemId(null)
          }}
          itemId={selectedItemId}
          productionId={productionId}
        />
      )}
    </Flex>
  )
}

export default SettlementTab
