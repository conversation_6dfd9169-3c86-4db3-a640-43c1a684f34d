import { 
  Card, 
  List, 
  Empty, 
  Space, 
  Tag, 
  Typography, 
  Button, 
  Popconfirm, 
  message, 
  Image,
  Flex,
  Descriptions
} from 'antd'
import { 
  PlusOutlined, 
  DeleteOutlined, 
  FileImageOutlined, 
  DollarOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons'
import React, { useState } from 'react'
import { IPrProductionSettlementItem, IPrProductionSettlementDetail } from '../store'
import useProjectDetailStore from '../store'
import AddSettlementDetailModal from './AddSettlementDetailModal'
import dayjs from 'dayjs'

const { Text } = Typography

interface ISettlementPaymentDetailProps {
  settlementItem: IPrProductionSettlementItem | null
  loading: boolean
  onRefresh: () => void
  productionId: number
}

const SettlementPaymentDetail: React.FC<ISettlementPaymentDetailProps> = ({
  settlementItem,
  loading,
  onRefresh,
  productionId,
}) => {
  const [addModalOpen, setAddModalOpen] = useState(false)
  const [deleteLoading, setDeleteLoading] = useState<number | null>(null)

  const { deleteSettlementDetail } = useProjectDetailStore()

  // 处理删除支付明细
  const handleDelete = async (detailId: number) => {
    setDeleteLoading(detailId)
    try {
      const success = await deleteSettlementDetail(detailId)
      if (success) {
        message.success('删除成功')
        onRefresh()
      } else {
        message.error('删除失败')
      }
    } catch (error) {
      console.error('删除支付明细失败:', error)
      message.error('删除失败')
    } finally {
      setDeleteLoading(null)
    }
  }

  // 处理添加成功
  const handleAddSuccess = () => {
    setAddModalOpen(false)
    onRefresh()
  }

  // 获取状态标签
  const getStatusTag = (status?: number) => {
    switch (status) {
      case 0:
        return <Tag icon={<ClockCircleOutlined />} color="orange">待审核</Tag>
      case 1:
        return <Tag icon={<CheckCircleOutlined />} color="green">通过</Tag>
      case 2:
        return <Tag icon={<CloseCircleOutlined />} color="red">不通过</Tag>
      default:
        return <Tag color="default">未知</Tag>
    }
  }

  // 获取支付方式标签
  const getPaymentMethodLabel = (method?: string) => {
    switch (method) {
      case '1':
        return '转账'
      case '2':
        return '平台'
      default:
        return method || '未知'
    }
  }

  // 渲染支付明细项
  const renderPaymentItem = (item: IPrProductionSettlementDetail) => {
    return (
      <List.Item
        actions={[
          <Popconfirm
            key="delete"
            title="确认删除"
            description="删除后无法恢复，确认删除该支付明细吗？"
            onConfirm={() => handleDelete(item.id!)}
            okText="确认"
            cancelText="取消">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              loading={deleteLoading === item.id}
              size="small">
              删除
            </Button>
          </Popconfirm>
        ]}>
        <Card size="small" style={{ width: '100%' }}>
          <Flex justify="space-between" align="flex-start">
            <Space direction="vertical" size={8} style={{ flex: 1 }}>
              <Flex align="center" gap={12}>
                <Text strong style={{ fontSize: 16 }}>
                  ¥{(item.amount || 0).toLocaleString()}
                </Text>
                {getStatusTag(item.status)}
                <Tag color="blue">
                  {getPaymentMethodLabel(item.paymentMethod)}
                </Tag>
                {item.isApprovalInitiated && (
                  <Tag color="purple">已发起审批</Tag>
                )}
              </Flex>
              
              {item.description && (
                <Text type="secondary">{item.description}</Text>
              )}
              
              <Space size={0} split={<span style={{ margin: '0 8px', color: '#d9d9d9' }}>|</span>}>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  创建时间: {item.createTime ? dayjs(item.createTime).format('YYYY-MM-DD HH:mm') : '-'}
                </Text>
                {item.updateTime && (
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    更新时间: {dayjs(item.updateTime).format('YYYY-MM-DD HH:mm')}
                  </Text>
                )}
              </Space>
            </Space>

            {/* 票据图片 */}
            {item.invoicePathList && item.invoicePathList.length > 0 && (
              <Space direction="vertical" align="center">
                <Text type="secondary" style={{ fontSize: 12 }}>
                  <FileImageOutlined /> 票据 ({item.invoicePathList.length})
                </Text>
                <Image.PreviewGroup>
                  <Space wrap size={4}>
                    {item.invoicePathList.slice(0, 3).map((path, index) => (
                      <Image
                        key={index}
                        width={40}
                        height={40}
                        src={path}
                        style={{ objectFit: 'cover', borderRadius: 4 }}
                        placeholder={
                          <div style={{ 
                            width: 40, 
                            height: 40, 
                            background: '#f5f5f5', 
                            borderRadius: 4,
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}>
                            <FileImageOutlined style={{ color: '#bfbfbf' }} />
                          </div>
                        }
                      />
                    ))}
                    {item.invoicePathList.length > 3 && (
                      <div style={{
                        width: 40,
                        height: 40,
                        background: '#f5f5f5',
                        borderRadius: 4,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: 12,
                        color: '#666'
                      }}>
                        +{item.invoicePathList.length - 3}
                      </div>
                    )}
                  </Space>
                </Image.PreviewGroup>
              </Space>
            )}
          </Flex>
        </Card>
      </List.Item>
    )
  }

  if (loading) {
    return <Card loading />
  }

  if (!settlementItem) {
    return (
      <Empty
        description="暂无数据"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    )
  }

  const paymentDetails = settlementItem.settlementDetail || []
  const totalPaid = paymentDetails.reduce((sum, item) => sum + (item.amount || 0), 0)
  const remainingAmount = (settlementItem.totalPrice || 0) - totalPaid

  return (
    <Space direction="vertical" size={16} style={{ width: '100%' }}>
      {/* 支付统计 */}
      <Card
        title={
          <Space>
            <DollarOutlined />
            <Text strong>支付统计</Text>
          </Space>
        }
        size="small">
        <Descriptions column={3} size="small" colon={false}>
          <Descriptions.Item label="结算总额">
            <Text strong>¥{(settlementItem.totalPrice || 0).toLocaleString()}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="已支付">
            <Text strong type="success">¥{totalPaid.toLocaleString()}</Text>
          </Descriptions.Item>
          <Descriptions.Item label="待支付">
            <Text strong type={remainingAmount > 0 ? 'warning' : 'success'}>
              ¥{remainingAmount.toLocaleString()}
            </Text>
          </Descriptions.Item>
        </Descriptions>
      </Card>

      {/* 支付明细列表 */}
      <Card
        title={
          <Space>
            <FileImageOutlined />
            <Text strong>支付明细</Text>
          </Space>
        }
        size="small"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            size="small"
            onClick={() => setAddModalOpen(true)}>
            添加明细
          </Button>
        }>
        {paymentDetails.length > 0 ? (
          <List
            dataSource={paymentDetails}
            renderItem={renderPaymentItem}
            size="small"
            split={false}
          />
        ) : (
          <Empty
            description="暂无支付明细"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        )}
      </Card>

      {/* 添加支付明细弹窗 */}
      {addModalOpen && settlementItem?.id && (
        <AddSettlementDetailModal
          open={addModalOpen}
          onCancel={() => setAddModalOpen(false)}
          onSuccess={handleAddSuccess}
          itemId={settlementItem.id}
          maxAmount={remainingAmount}
        />
      )}
    </Space>
  )
}

export default SettlementPaymentDetail
