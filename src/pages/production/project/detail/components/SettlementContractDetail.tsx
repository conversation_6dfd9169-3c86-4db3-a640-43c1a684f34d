import { Card, Descriptions, Empty, Space, Tag, Typography, Button } from 'antd'
import { FileTextOutlined, EyeOutlined } from '@ant-design/icons'
import React from 'react'
import { IPrProductionSettlementItem } from '../store'
import { getBudgetCategoryLabel, getBudgetSubcategoryLabel } from '../../../../../consts/budget'

const { Text } = Typography

interface ISettlementContractDetailProps {
  settlementItem: IPrProductionSettlementItem | null
  loading: boolean
}

const SettlementContractDetail: React.FC<ISettlementContractDetailProps> = ({
  settlementItem,
  loading,
}) => {
  if (loading) {
    return <Card loading />
  }

  if (!settlementItem) {
    return (
      <Empty
        description="暂无数据"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    )
  }

  // 处理合同状态显示
  const getContractStatusTag = (status?: number) => {
    switch (status) {
      case 0:
        return <Tag color="orange">待签署</Tag>
      case 1:
        return <Tag color="green">已签署</Tag>
      case 2:
        return <Tag color="red">已拒绝</Tag>
      default:
        return <Tag color="default">未知</Tag>
    }
  }

  // 处理合同类型显示
  const getContractTypeLabel = (verify?: number) => {
    switch (verify) {
      case 101:
        return '保密协议'
      case 102:
        return '短剧演员剧组聘用合同'
      default:
        return '其他合同'
    }
  }

  // 处理人员类型显示
  const getAuthorTypeLabel = (authorType?: number) => {
    switch (authorType) {
      case 0:
        return '人员'
      case 1:
        return '合作商'
      default:
        return '未知'
    }
  }

  return (
    <Space direction="vertical" size={16} style={{ width: '100%' }}>
      {/* 结算项目基本信息 */}
      <Card
        title={
          <Space>
            <FileTextOutlined />
            <Text strong>结算项目信息</Text>
          </Space>
        }
        size="small">
        <Descriptions column={2} size="small" colon={false}>
          <Descriptions.Item label="项目名称">
            <Space>
              {settlementItem.itemName}
              {settlementItem.isPackage ? (
                <Tag color="volcano">打包</Tag>
              ) : (
                <Tag color="cyan">单项</Tag>
              )}
            </Space>
          </Descriptions.Item>
          <Descriptions.Item label="总价">
            <Text strong type="danger">
              ¥{(settlementItem.totalPrice || 0).toLocaleString()}
            </Text>
          </Descriptions.Item>
          {settlementItem.quotedPrice && (
            <Descriptions.Item label="单价">
              ¥{settlementItem.quotedPrice.toLocaleString()}
            </Descriptions.Item>
          )}
          {settlementItem.dayCount && (
            <Descriptions.Item label="天数">
              {settlementItem.dayCount}
            </Descriptions.Item>
          )}
          {settlementItem.rate && (
            <Descriptions.Item label="费率">
              {settlementItem.rate}%
            </Descriptions.Item>
          )}
          <Descriptions.Item label="发票">
            {settlementItem.hasInvoice ? '有' : '无'}
          </Descriptions.Item>
          {settlementItem.description && (
            <Descriptions.Item label="备注" span={2}>
              {settlementItem.description}
            </Descriptions.Item>
          )}
        </Descriptions>
      </Card>

      {/* 项目详情（如果是打包项目） */}
      {settlementItem.isPackage && settlementItem.itemsDetail && settlementItem.itemsDetail.length > 0 && (
        <Card
          title={
            <Space>
              <FileTextOutlined />
              <Text strong>包含项目详情</Text>
            </Space>
          }
          size="small">
          <Space direction="vertical" size={12} style={{ width: '100%' }}>
            {settlementItem.itemsDetail.map((detail, index) => (
              <Card key={index} type="inner" size="small">
                <Space wrap>
                  <Tag color="geekblue">
                    {getBudgetCategoryLabel(detail.category)}
                  </Tag>
                  <Text strong>
                    {detail.subcategoryLabel || getBudgetSubcategoryLabel(detail.subcategory)}
                  </Text>
                  {detail.personCount > 0 && (
                    <Tag color="orange">
                      {detail.personCount}人
                    </Tag>
                  )}
                </Space>
              </Card>
            ))}
          </Space>
        </Card>
      )}

      {/* 合同信息 */}
      {settlementItem.contract ? (
        <Card
          title={
            <Space>
              <FileTextOutlined />
              <Text strong>合同信息</Text>
            </Space>
          }
          size="small">
          <Descriptions column={2} size="small" colon={false}>
            <Descriptions.Item label="合同编号">
              {settlementItem.contract.contractNumb || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="合同名称">
              {settlementItem.contract.contractName || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="合同类型">
              {getContractTypeLabel(settlementItem.contract.verify)}
            </Descriptions.Item>
            <Descriptions.Item label="合同状态">
              {getContractStatusTag(settlementItem.contract.status)}
            </Descriptions.Item>
            <Descriptions.Item label="签约人">
              {settlementItem.contract.penNames || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="人员类型">
              {getAuthorTypeLabel(settlementItem.contract.authorType)}
            </Descriptions.Item>
            {settlementItem.contract.formalFileView && (
              <Descriptions.Item label="合同文件" span={2}>
                <Button
                  type="link"
                  icon={<EyeOutlined />}
                  onClick={() => window.open(settlementItem.contract?.formalFileView, '_blank')}>
                  查看合同
                </Button>
              </Descriptions.Item>
            )}
          </Descriptions>
        </Card>
      ) : (
        <Card
          title={
            <Space>
              <FileTextOutlined />
              <Text strong>合同信息</Text>
            </Space>
          }
          size="small">
          <Empty
            description="暂无关联合同"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          />
        </Card>
      )}
    </Space>
  )
}

export default SettlementContractDetail
