import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons'
import { <PERSON><PERSON>, Card, Drawer, Flex, Form, Input, InputNumber, message, Select, Space, Switch } from 'antd'
import React, { useEffect } from 'react'
import {
  BUDGET_CATEGORY_OPTIONS,
  getSubcategoryOptionsByCategory,
} from '../../../../../consts/budget'
import { IProductionListItem } from '../../list/store'
import useProjectDetailStore, { IPrProductionBudgetItem } from '../store'

const { TextArea } = Input

interface IAddNewBudgetPackageProps {
  open: boolean
  onCancel: () => void
  onSuccess: () => void
  loading?: boolean
  productionId: number
  project?: IProductionListItem
  editingItem?: IPrProductionBudgetItem // 编辑模式时传入的数据
  existingSubcategories?: Set<number> // 已存在的二级分类，用于防重复
  parentType?: number // 类目类型（0预算，1结算）
}

const AddNewBudgetPackage: React.FC<IAddNewBudgetPackageProps> = ({
  open,
  onCancel,
  onSuccess,
  loading = false,
  productionId,
  project,
  editingItem,
  existingSubcategories = new Set(),
  parentType = 0, // 默认为预算类型
}) => {
  const [form] = Form.useForm()
  const { saveBudgetItems } = useProjectDetailStore()

  // 判断是否为编辑模式
  const isEdit = !!editingItem?.id

  // 监听表单中的 itemsDetail 变化
  const formItemsDetail = Form.useWatch('itemsDetail', form)

  // 监听一级分类变化，清空二级分类
  const handleCategoryChange = (name: number) => {
    form.setFieldValue(['itemsDetail', name, 'subcategory'], undefined)
  }

  // 处理 isMain 变化，确保只有一个项目可以设置为主要项目
  const handleIsMainChange = (currentIndex: number, checked: boolean) => {
    if (checked) {
      // 如果当前项目设置为主要项目，取消其他项目的主要状态
      const currentItemsDetail = form.getFieldValue('itemsDetail') || []
      const updatedItemsDetail = currentItemsDetail.map((item: any, index: number) => ({
        ...item,
        isMain: index === currentIndex
      }))
      form.setFieldValue('itemsDetail', updatedItemsDetail)
    } else {
      // 如果取消当前项目的主要状态，直接更新
      form.setFieldValue(['itemsDetail', currentIndex, 'isMain'], false)
    }
  }

  // 获取过滤后的二级分类选项（排除已存在的）
  const getFilteredSubcategoryOptions = (category: number) => {
    const allOptions = getSubcategoryOptionsByCategory(category)
    // 在编辑模式下，不过滤当前编辑项的subcategory
    if (isEdit && editingItem?.itemsDetail) {
      const currentSubcategories = new Set(editingItem.itemsDetail.map(item => item.subcategory))
      return allOptions.filter(option =>
        !existingSubcategories.has(option.value) || currentSubcategories.has(option.value)
      )
    }
    // 新增模式下，过滤所有已存在的subcategory
    return allOptions.filter(option => !existingSubcategories.has(option.value))
  }



  useEffect(() => {
    if (open) {
      if (isEdit && editingItem) {
        // 编辑模式：填充现有数据
        form.setFieldsValue({
          itemName: editingItem.itemName || '',
          quotedPrice: editingItem.quotedPrice || undefined,
          personCount: editingItem.personCount || undefined,
          dayCount: editingItem.dayCount || undefined,
          totalPrice: editingItem.totalPrice,
          hasInvoice: editingItem.hasInvoice === 1,
          description: editingItem.description || '',
          rate: (editingItem as any).rate || undefined, // 结算模式下的费率
          // 填充itemsDetail列表
          itemsDetail: editingItem.itemsDetail?.map(detail => ({
            id: detail.category,
            itemId: detail.category,
            category: detail.category,
            subcategory: detail.subcategory,
            personCount: detail.personCount || undefined,
            isMain: detail.isMain || false,
          })) || [],
        })
      } else {
        // 新增模式：重置表单并设置默认值
        form.resetFields()
        form.setFieldsValue({
          personCount: null,
          dayCount: null,
          hasInvoice: false,
          rate: undefined,
          itemsDetail: [{ category: undefined, subcategory: undefined, personCount: null, isMain: true }],
        })
      }
    }
  }, [open, isEdit, editingItem, form])

  // 处理保存
  const handleSave = async (values: any) => {
    try {
      // 验证itemsDetail
      if (!values.itemsDetail || values.itemsDetail.length === 0) {
        message.error('请至少添加一个预算子项')
        return
      }

      // 验证每个子项的必填字段
      const invalidItems = values.itemsDetail.filter((item: any) => !item.category || !item.subcategory)
      if (invalidItems.length > 0) {
        message.error('请完善所有预算子项的分类信息')
        return
      }

      // 验证是否有主要项目
      const mainItems = values.itemsDetail.filter((item: any) => item.isMain)
      if (mainItems.length === 0) {
        message.error('请至少设置一个主要项目')
        return
      }
      if (mainItems.length > 1) {
        message.error('只能设置一个主要项目')
        return
      }

      // 验证是否有重复的二级分类（在新增模式下）
      if (!isEdit) {
        const subcategories = values.itemsDetail.map((item: any) => item.subcategory)
        const duplicateSubcategories = subcategories.filter((subcategory: number, index: number) =>
          subcategories.indexOf(subcategory) !== index
        )
        if (duplicateSubcategories.length > 0) {
          message.error('不能添加重复的二级分类')
          return
        }

        // 验证是否与已存在的二级分类重复
        const conflictSubcategories = subcategories.filter((subcategory: number) =>
          existingSubcategories.has(subcategory)
        )
        if (conflictSubcategories.length > 0) {
          message.error('选择的二级分类已存在，请选择其他分类')
          return
        }
      }

      const budgetItem: any = {
        id: editingItem?.id,
        productionId,
        isPackage: true,
        itemName: values.itemName,
        quotedPrice: values.quotedPrice || 0,
        personCount: values.personCount || 0,
        dayCount: values.dayCount || 0,
        totalPrice: values.totalPrice,
        hasInvoice: values.hasInvoice ? 1 : 0,
        description: values.description,
        parentType,
        rate: values.rate || 0, // 结算模式下添加费率
        settlementDetail: editingItem?.settlementDetail || [],
        itemsDetail: values.itemsDetail.map((item: any) => ({
          id: item.id || 0,
          itemId: editingItem?.id || 0, // 新增时为0，后端会处理
          category: item.category,
          subcategory: item.subcategory,
          personCount: item.personCount || 0, // 如果子项没有设置人数，使用总人数
          isMain: !!item.isMain
        })),
      }

      const success = await saveBudgetItems({
        productionId,
        budgetItems: [budgetItem],
      })

      if (success) {
        message.success(isEdit ? '编辑打包项成功' : '添加打包项成功')
        onSuccess()
        onCancel()
      } else {
        message.error(isEdit ? '编辑打包项失败' : '添加打包项失败')
      }
    } catch (error) {
      console.error('保存打包项失败:', error)
      message.error(isEdit ? '编辑打包项失败' : '添加打包项失败')
    }
  }

  return (
    <Drawer
      title={isEdit ? '编辑打包项' : '添加打包项'}
      open={open}
      onClose={onCancel}
      width={800}
      extra={
        <Space>
          <Button onClick={onCancel}>取消</Button>
          <Button type="primary" loading={loading} onClick={() => form.submit()}>
            {isEdit ? '保存' : '添加'}
          </Button>
        </Space>
      }>
      <Form form={form} layout="vertical" onFinish={handleSave}>
        <Form.Item
          label="打包名称"
          name="itemName"
          rules={[{ required: true, message: '请输入打包名称' }]}>
          <Input placeholder="请输入打包名称" />
        </Form.Item>

        <Form.Item label="预算子项">
          <Form.List name="itemsDetail">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Card
                    key={key}
                    size="small"
                    title={`子项 ${name + 1}`}
                    extra={
                      fields.length > 1 ? (
                        <Button
                          type="link"
                          danger
                          icon={<MinusCircleOutlined />}
                          onClick={() => remove(name)}>
                          删除
                        </Button>
                      ) : null
                    }
                    style={{ marginBottom: 16 }}>
                    <Flex gap={16}>
                      <Form.Item
                        {...restField}
                        name={[name, 'category']}
                        label="一级分类"
                        rules={[{ required: true, message: '请选择一级分类' }]}
                        style={{ flex: 1 }}>
                        <Select
                          placeholder="请选择一级分类"
                          options={BUDGET_CATEGORY_OPTIONS}
                          onChange={() => handleCategoryChange(name)}
                          disabled={isEdit}
                        />
                      </Form.Item>

                      <Form.Item
                        {...restField}
                        name={[name, 'subcategory']}
                        label="二级分类"
                        rules={[{ required: true, message: '请选择二级分类' }]}
                        style={{ flex: 1 }}>
                        <Select
                          placeholder="请选择二级分类"
                          options={formItemsDetail?.[name]?.category ? getFilteredSubcategoryOptions(formItemsDetail[name].category) : []}
                          disabled={isEdit}
                        />
                      </Form.Item>

                      <Form.Item
                        {...restField}
                        name={[name, 'isMain']}
                        label="主要项目"
                        valuePropName="checked"
                        style={{ flex: 1 }}>
                        <Switch
                          onChange={(checked) => handleIsMainChange(name, checked)}
                        />
                      </Form.Item>

                      <Form.Item
                        {...restField}
                        name={[name, 'personCount']}
                        label="人数"
                        style={{ flex: 1 }}>
                        <InputNumber min={1} placeholder="人数" style={{ width: '100%' }} />
                      </Form.Item>
                    </Flex>
                  </Card>
                ))}
                {isEdit ? null : <Form.Item>
                  <Button
                    type="dashed"
                    onClick={() => add({ category: undefined, subcategory: undefined, personCount: undefined, isMain: false })}
                    block
                    icon={<PlusOutlined />}>
                    添加预算子项
                  </Button>
                </Form.Item>}
              </>
            )}
          </Form.List>
        </Form.Item>

        <Flex gap={16}>
          <Form.Item
            label={`单价 (${project?.currencySymbol || '¥'})`}
            name="quotedPrice"
            style={{ flex: 1 }}
          >
            <InputNumber
              placeholder="请输入单价"
              min={0}
              precision={2}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            label="人数"
            name="personCount"
            style={{ flex: 1 }}
          >
            <InputNumber
              placeholder="请输入人数"
              min={1}
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            label="天数"
            name="dayCount"
            style={{ flex: 1 }}
          >
            <InputNumber
              placeholder="请输入天数"
              min={1}
              style={{ width: '100%' }}
            />
          </Form.Item>
        </Flex>

        <Form.Item
          label={`总价 (${project?.currencySymbol || '¥'})`}
          name="totalPrice"
          rules={[{ required: true, message: '请输入总价' }]}>
          <InputNumber
            placeholder="请输入总价"
            min={0}
            precision={2}
            style={{ width: '100%' }}
          />
        </Form.Item>

        {/* 结算模式下显示费率字段 */}
        {parentType === 1 && (
          <Form.Item
            label="费率 (%)"
            name="rate"
            rules={[{ required: true, message: '请输入费率' }]}>
            <InputNumber
              placeholder="请输入费率"
              min={0}
              max={100}
              precision={2}
              style={{ width: '100%' }}
              addonAfter="%"
            />
          </Form.Item>
        )}

        <Form.Item label="是否有发票" name="hasInvoice" valuePropName="checked">
          <Switch />
        </Form.Item>

        <Form.Item label="备注说明" name="description">
          <TextArea
            placeholder="请输入备注说明"
            rows={3}
            maxLength={500}
            showCount
          />
        </Form.Item>
      </Form>
    </Drawer>
  )
}

export default AddNewBudgetPackage
