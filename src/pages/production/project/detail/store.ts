import { create } from 'zustand'
import { post, get as requestGet } from '../../../../utils/request'
import type { BudgetCategory, BudgetSubcategory } from '../../../../consts/budget'

// 预算类目信息（对应API的PrProductionBudgetItems）
export interface IPrProductionBudgetItem {
  id?: number // 主键ID
  parentType: number // 类目类型（0预算，1结算）
  productionId: number // 关联项目ID
  isPackage?: boolean // 是否打包
  itemName?: string // 打包名称
  quotedPrice?: number // 单价
  personCount: number // 人数
  dayCount: number // 拍摄天数
  totalPrice?: number // 总金额
  hasInvoice?: number // 是否正规发票（1=是，0=否）
  description?: string // 备注说明
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  itemsDetail?: IPrProductionItemsDetail[] // 打包子类列表
  contractId?: number // 合同Id
  rate?: number // 费率（如税率）
  settlementDetail?: IPrProductionSettlementDetail[] // 支出明细
}

// 打包子类信息（对应API的PrProductionItemsDetail）
export interface IPrProductionItemsDetail {
  id?: number // 主键ID
  itemId: number // 打包项Id
  category: BudgetCategory // 一级分类
  subcategory: BudgetSubcategory // 二级分类
  personCount: number // 人数
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
  categoryLabel?: string // 一级分类标签（只读）
  subcategoryLabel?: string // 二级分类标签（只读）
  isMain?: boolean
}

// 获取预算项目参数（对应API的GetBudgetItems）
export interface IGetBudgetItemsParams {
  productionId: number // 项目ID
}

// 保存预算类目参数（对应API的SaveProductionBudgetItemsDto）
export interface ISaveProductionBudgetItemsDto {
  productionId: number // 项目ID
  budgetItems?: IPrProductionBudgetItem[] // 预算类目列表
}

// 合同信息（对应API的CommContractDto）
export interface ICommContractDto {
  id?: number // 合同Id
  contractNumb?: string // 合同编号
  contractName?: string // 合同名称
  authorId?: number // 人员id
  authorType?: number // 人员类型 0人员，1合作商
  verify?: number // 合同类型名称 101保密协议,102短剧演员剧组聘用合同
  tempFile?: string // 文件地址
  formalFileView?: string // 文件预览地址
  penNames?: string // 人员名称
  bookId?: string // 项目id
  status?: number // 合同状态
}

// 结算类目信息（对应API的PrProductionSettlementItemsDto）
// 继承预算类目，因为两者共用一个表
export interface IPrProductionSettlementItem extends IPrProductionBudgetItem {
  contractId?: number // 合同Id
  rate?: number // 费率（如税率）
  settlementDetail?: IPrProductionSettlementDetail[] // 支出明细
  contract?: ICommContractDto // 合同信息
}

// 结算类目明细（对应API的PrProductionSettlementDetail）
export interface IPrProductionSettlementDetail {
  id?: number // ID
  itemId: number // 项Id
  amount?: number // 金额
  status?: number // 状态（0待审核，1通过，2不通过）
  invoicePath?: string // 票据地址（多个逗号分割）
  invoicePathList?: string[] // 票据地址数组（前端使用）
  paymentMethod?: string // 支付方式(1转账、2平台)
  description?: string // 备注说明
  isApprovalInitiated?: boolean // 是否发起审批
  createTime?: string // 创建时间
  updateTime?: string // 更新时间
}

// 获取结算项目参数
export interface IGetSettlementItemsParams {
  productionId: number // 项目ID
}

// API响应基础结构
export interface IWebResponseContent<T = any> {
  status: boolean
  code?: string
  message?: string
  data?: T
}

export interface IProjectDetailStore {
  /* 预算相关API调用方法 */

  // 获取预算项目列表
  getBudgetItems: (params: IGetBudgetItemsParams) => Promise<IPrProductionBudgetItem[] | null>

  // 根据ID获取预算项目详情
  getBudgetItemsById: (id: number) => Promise<IPrProductionBudgetItem | null>

  // 保存预算项目信息
  saveBudgetItems: (params: ISaveProductionBudgetItemsDto) => Promise<boolean>

  // 删除预算项目
  deleteBudgetItems: (id: number) => Promise<boolean>

  /* 结算相关API调用方法 */

  // 获取结算项目列表
  getSettlementItems: (params: IGetSettlementItemsParams) => Promise<IPrProductionBudgetItem[] | null>

  // 根据ID获取结算项目详情
  getSettlementItemsById: (id: number) => Promise<IPrProductionBudgetItem | null>

  // 添加项目支付明细
  addSettlementDetail: (params: IPrProductionSettlementDetail) => Promise<boolean>

  // 删除项目支付明细
  deleteSettlementDetail: (id: number) => Promise<boolean>
}

export default create<IProjectDetailStore>(() => ({
  // 获取预算项目列表
  getBudgetItems: async (params: IGetBudgetItemsParams) => {
    try {
      const { data, status } = await post<IWebResponseContent<IPrProductionBudgetItem[]>, any>(
        '/ProductionFinance/GetBudgetItems',
        null,
        { params },
      )
      if (status && data?.dataList) {
        return data.dataList || []
      }

      return []
    } catch (error) {
      console.error('获取预算项目列表失败:', error)

      return []
    }
  },

  // 根据ID获取预算项目详情
  getBudgetItemsById: async (id: number) => {
    try {
      const { data, status } = await requestGet<IWebResponseContent<IPrProductionBudgetItem>, any>(
        `/ProductionFinance/GetBudgetItemsById?id=${id}`
      )

      if (status && data?.data) {
        return data.data
      }

      return null
    } catch (error) {
      console.error('获取预算项目详情失败:', error)

      return null
    }
  },

  // 保存预算项目信息
  saveBudgetItems: async (params: ISaveProductionBudgetItemsDto) => {
    try {
      const { data, status } = await post<IWebResponseContent, any>('/ProductionFinance/Save', params)

      return !!(status && data?.status)
    } catch (error) {
      console.error('保存预算项目信息失败:', error)

      return false
    }
  },

  // 删除预算项目
  deleteBudgetItems: async (id: number) => {
    try {
      const { data, status } = await requestGet<IWebResponseContent, any>(
        `/ProductionFinance/DeleteBudgetItems?id=${id}`
      )

      return !!(status && data?.status)
    } catch (error) {
      console.error('删除预算项目失败:', error)

      return false
    }
  },

  // 获取结算项目列表
  getSettlementItems: async (params: IGetSettlementItemsParams) => {
    try {
      const { data, status } = await post<IWebResponseContent<IPrProductionBudgetItem>, any>(
        '/ProductionFinance/GetSettlementItems',
        null,
        { params },
      )
      if (status && data?.dataList) {
        // API返回单个对象，包装成数组返回以保持一致性
        return data.dataList||[]
      }

      return []
    } catch (error) {
      console.error('获取结算项目列表失败:', error)

      return []
    }
  },

  // 根据ID获取结算项目详情
  getSettlementItemsById: async (id: number) => {
    try {
      const { data, status } = await requestGet<IWebResponseContent<IPrProductionBudgetItem>, any>(
        `/ProductionFinance/GetSettlementItemsById?id=${id}`
      )
      if (status && data?.dataList) {
        // 处理 invoicePath 字段，将逗号分割的字符串转换为数组
        const result = data.dataList
        if (result.settlementDetail) {
          result.settlementDetail = result.settlementDetail.map((detail: IPrProductionSettlementDetail) => ({
            ...detail,
            invoicePathList: detail.invoicePath ? detail.invoicePath.split(',').filter(Boolean) : []
          }))
        }
        return result
      }

      return null
    } catch (error) {
      console.error('获取结算项目详情失败:', error)

      return null
    }
  },

  // 添加项目支付明细
  addSettlementDetail: async (params: IPrProductionSettlementDetail) => {
    try {
      // 处理 invoicePathList 字段，将数组转换为逗号分割的字符串
      const submitParams = {
        ...params,
        invoicePath: params.invoicePathList ? params.invoicePathList.join(',') : params.invoicePath
      }
      delete submitParams.invoicePathList

      const { data, status } = await post<IWebResponseContent, any>(
        '/ProductionFinance/AddSettlementDetail',
        submitParams
      )

      return !!(status && data?.status)
    } catch (error) {
      console.error('添加项目支付明细失败:', error)

      return false
    }
  },

  // 删除项目支付明细
  deleteSettlementDetail: async (id: number) => {
    try {
      const { data, status } = await requestGet<IWebResponseContent, any>(
        `/ProductionFinance/DeleteSettlementDetail?id=${id}`
      )

      return !!(status && data?.status)
    } catch (error) {
      console.error('删除项目支付明细失败:', error)

      return false
    }
  },
}))