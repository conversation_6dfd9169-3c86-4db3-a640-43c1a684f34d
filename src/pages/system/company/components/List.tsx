import { Divider, Flex, Space, Table, Typography } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import dayjs from 'dayjs'
import React from 'react'
import { DATE_FORMAT_BASE } from '../../../../consts/date'
import { ICompanyListItem, IOperateType } from '../store'

const { Text } = Typography

interface IPagination {
  current: number
  pageSize: number
  total: number
}

interface IListProps {
  data: ICompanyListItem[]
  loading: boolean
  pagination: IPagination
  onOperate: (type: IOperateType, data?: ICompanyListItem) => void
  onChange: (current: number, pageSize: number) => void
}

const CompanyList: React.FC<IListProps> = ({ data, loading, pagination, onOperate, onChange }) => {
  const columns: ColumnsType<ICompanyListItem> = [
    {
      title: '公司',
      dataIndex: 'companyName',
      key: 'companyName',
      width: 350,
      ellipsis: true,
      align: 'center',
      render: (text: string, record) => (
        <Flex vertical gap={6} className="text-left">
          <Text strong>{text || '-'}</Text>
          {record.companyAddress && (
            <Typography.Text type="secondary" ellipsis>
              {record.companyAddress}
            </Typography.Text>
          )}
        </Flex>
      ),
    },
    {
      title: '简称',
      dataIndex: 'companyShort',
      key: 'companyShort',
      width: 120,
      align: 'center',
      render: (text: string) => text || '-',
    },
    {
      title: '编码',
      dataIndex: 'companyCode',
      key: 'companyCode',
      width: 200,
      align: 'center',
      render: (text: string) => text || '-',
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
      align: 'center',
      render: (text: string) => text || '-',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 180,
      ellipsis: true,
      align: 'center',
      render: (text: string) => text || '-',
    },
    {
      title: '最近更新',
      dataIndex: 'lastModifier',
      key: 'lastModifier',
      width: 160,
      align: 'center',
      render: (lastModifier: string, item) => (
        <Space direction="vertical">
          <span>{lastModifier}</span>
          {item.updateTime && <span>{dayjs(item.updateTime).format(DATE_FORMAT_BASE)}</span>}
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      align: 'center',
      render: (_, record) => (
        <Space size={0} split={<Divider type="vertical" />}>
          <Typography.Link onClick={() => onOperate('edit', record)}>编辑</Typography.Link>
          <Typography.Link onClick={() => onOperate('view', record)}>查看</Typography.Link>
        </Space>
      ),
    },
  ]

  return (
    <Table
      rowKey="id"
      columns={columns}
      dataSource={data}
      loading={loading}
      pagination={{
        ...pagination,
        showSizeChanger: true,
        showQuickJumper: true,
        onChange,
      }}
      sticky={{ offsetHeader: -24 }}
    />
  )
}

export default CompanyList
