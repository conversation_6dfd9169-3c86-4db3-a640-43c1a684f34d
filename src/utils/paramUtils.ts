import dayjs from 'dayjs'

export default {
  // 过滤 ""、null、unde 属性
  filterEmptyProps(source: Record<string, any>) {
    const target = {}

    for (const key in source) {
      if (source[key] !== '' && source[key] !== void 0 && source[key] !== null) {
        target[key] = source[key]
      }
    }

    return target
  },
  // 格式化系统
  formatOS(os: string) {
    switch (os) {
      case 'ios':
        return 'iOS'
      case 'android':
        return 'Android'
      case 'windows':
        return 'Windows'
      default:
        return os
    }
  },

  // number类型转换
  number(value: any) {
    if (value) {
      return Number(value)
    }

    return void 0
  },

  /**
   * 1.如果 value 不存在（即为 null, undefined, 空字符串等假值），则返回一个空数组 []。
   * 2.如果 value 是一个数组，就对数组中的每个元素进行处理：如果 type 是 'number' 类型，则将每个元素转换为数字，否则保持不变并返回新的数组。
   * 3.如果 value 不是数组（单个值），则根据 type 的类型进行处理：如果 type 是 'number' 类型，则将其转换为数字放入数组中，否则保持不变，并返回包含此单个元素的数组。
   */
  array(value: any, type = 'number') {
    if (!value) {
      return []
    }

    const result = Array.isArray(value)
      ? value.map(v => (type === 'number' ? Number(v) : v))
      : [type === 'number' ? Number(value) : value]

    return result
  },

  /**
   *
   * @param keys 是一个字符串数组，包含需要转换为数字的字段名称
   * @param params 是一个包含各种参数的对象，其中可能包含了需要转换为数字的字段。
   *   函数会检查是否传入了有效的 keys 数组和 params 对象。
   *    对于每个在 keys 中的字段名：
   *      a. 如果该字段在 params 中对应的值是字符串类型，会将其转换为数字，并更新 params[key]。
   *      b. 如果该字段在 params 中对应的值是数组，会遍历数组中的每个元素，将每个元素都转换为数字，并更新 params[key]。
   *      最终，函数完成后，params 对象中包含了经过转换为数字类型后的相应字段值。
   */
  parseNumbers(keys: string[], params: { [k in string]: any }) {
    if (keys && params) {
      keys.forEach(key => {
        if (typeof params[key] === 'string') {
          params[key] = Number(params[key])
        }
        if (Array.isArray(params[key])) {
          params[key] = params[key].map((value: string) => Number(value))
        }
      })
    }
  },

  /**
   * @param keys 是一个字符串数组
   * @param params 包含各种参数的对象，其中可能包含了需要拆分为数组的字段
   */
  parseArrays(keys: string[], params: { [k in string]: any }) {
    // 检查是否传入了有效的 keys 数组和 params 对象
    if (keys && params) {
      keys.forEach(key => {
        //  a. 如果该字段在 params 中对应的值是字符串类型，函数将其使用逗号 , 分割成数组，并更新 params[key] 为新的数组
        if (typeof params[key] === 'string') {
          params[key] = params[key].split(',')
        }
      })
    }
  },

  /**
   *
   * @param keys 一个字符串数组，包含需要转换为 Day.js 对象的字段名称
   * @param params 包含各种参数的对象，其中可能包含了需要转换为 Day.js 对象的字段
   *  a. 如果该字段在 params 中对应的值是字符串类型，函数将其先转换为数字，然后使用 dayjs 函数创建对应的 Day.js 对象，并更新 params[key] 为新的 Day.js 对象。
   *  b. 如果该字段在 params 中对应的值是数组，函数将遍历数组中的每个元素，将每个元素都转换为数字并创建对应的 Day.js 对象，并更新 params[key] 为包含新的 Day.js 对象的数组
   *
   * Demo:
   * const keys = ['date1', 'date2'];
   *   const params = {
   *    date1: '1709337600000', // 3-2, 2024
   *    date2: ['1709251200000', '1709337600000'] // 3-1, 2024 and 3-2, 2024
   *  };
   *
   *  parseDayjs(keys, params);
   *
   *  console.log(params.date1.format('YYYY-MM-DD')); // 应输出 "2024-03-02"
   *  console.log(params.date2[0].format('YYYY-MM-DD')); // 应输出 "2024-03-01"
   *  console.log(params.date2[1].format('YYYY-MM-DD')); // 应输出 "2024-03-02"
   */
  parseDayjs(keys: string[], params: { [k in string]: any }) {
    // 检查是否传入了有效的 keys 数组和 params 对象
    if (keys && params) {
      keys.forEach(key => {
        if (typeof params[key] === 'string') {
          params[key] = dayjs(Number(params[key]))
        }
        if (Array.isArray(params[key])) {
          params[key] = params[key].map((value: string) => dayjs(Number(value)))
        }
      })
    }
  },
  // 移除URL 后面?参数
  removeQueryString(url: string) {
    if (typeof url !== 'string') {
      return url
    }

    const urlObj = new URL(url)
    urlObj.search = ''

    return urlObj.href
  },
}
