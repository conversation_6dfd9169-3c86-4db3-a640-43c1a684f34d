import { Image } from 'antd'
import React, { useMemo } from 'react'
import { envUrl } from '../utils/request'

interface EnvImageProps {
  src: string
  width?: number
  height?: number
  autoCrop?: boolean
  cropRate?: number
  [key: string]: any
}

const EnvImage: React.FC<EnvImageProps> = ({ src, autoCrop, cropRate = 1.5, width, height, ...rest }) => {
  const lastUrl = useMemo(() => {
    if (autoCrop) {
      let cacheSrc = src.replace(envUrl, '')
      cacheSrc = cacheSrc.startsWith('http') ? src : 'https://prg.51changdu.com' + src
      return (
        cacheSrc +
        `?imageMogr2/crop/${width ? width * cropRate : 120}x${height ? height * cropRate : 160}/gravity/center`
      )
    }

    return src.startsWith('http') ? src : envUrl + src
  }, [src, autoCrop])

  return <Image {...rest} src={lastUrl} width={width} height={height} />
}

export default EnvImage
