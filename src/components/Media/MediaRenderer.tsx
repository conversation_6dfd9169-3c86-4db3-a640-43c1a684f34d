import { MEDIA_TYPE_OPTIONS } from '@/consts'
import { envUrl } from '@/utils/request'
import {
  DownloadOutlined,
  EyeOutlined,
  FileOutlined,
  FilePdfOutlined,
  PictureOutlined,
  VideoCameraOutlined,
} from '@ant-design/icons'
import { Button, Divider, Flex, Modal, Space, Tag, Typography } from 'antd'
import React, { useState } from 'react'
import EnvImage from '../EnvImage'
import VideoView from '../VideoView'

interface MediaRendererProps {
  media: {
    id?: number
    mediaType: number
    mediaUrl?: string
    description?: string
    label?: string
    createTime?: string
    updateTime?: string
  }
  width?: number
  height?: number
  showActions?: boolean
  showInfo?: boolean
  showPreview?: boolean
}

const MediaRenderer: React.FC<MediaRendererProps> = ({
  media,
  width = 80,
  height = 60,
  showActions = false,
  showInfo = false,
  showPreview = true,
}) => {
  const [previewOpen, setPreviewOpen] = useState(false)
  const { component, label, icon, color } = MEDIA_TYPE_OPTIONS.find(option => option.value === media.mediaType) || {
    component: 'file',
    label: '未知类型',
    icon: 'FileOutlined',
    color: 'default',
  }

  if (!media.mediaUrl && component !== 'inputNumber') {
    return <Typography.Text type="secondary">暂无文件</Typography.Text>
  }

  // 解析URL数组
  const getUrlArray = (url?: string) => {
    if (!url) {
      return []
    }
    // 只有图片和视频类型才分割URL
    if (component === 'image' || component === 'video') {
      return url
        .split(',')
        .map(u => u.trim())
        .filter(Boolean)
    }

    return [url]
  }

  const urlArray = getUrlArray(media.mediaUrl)

  // 获取完整URL
  const getFullUrl = (url?: string) => {
    if (!url) {
      return ''
    }

    return url.startsWith('http') ? url : envUrl + url
  }

  // 渲染图标
  const renderIcon = () => {
    switch (icon) {
      case 'PictureOutlined':
        return <PictureOutlined />
      case 'VideoCameraOutlined':
        return <VideoCameraOutlined />
      case 'FileOutlined':
      default:
        return <FileOutlined />
    }
  }

  // 下载文件
  const handleDownload = (url?: string) => {
    const targetUrl = url ? getFullUrl(url) : getFullUrl(media.mediaUrl)

    if (targetUrl) {
      window.open(targetUrl, '_blank')
    }
  }
  const handleModalCancel = () => {
    setPreviewOpen(false)
  }

  // 预览文件
  const handlePreview = () => {
    if (showPreview) {
      setPreviewOpen(true)
    }
  }

  // 渲染内容
  const renderContent = () => {
    switch (component) {
      case 'image':
        return urlArray.map((url, index) => (
          <EnvImage
            key={index}
            src={url}
            width={width}
            height={height}
            className="radius img-cover hover-move"
            preview={showPreview ? { src: url } : false}
            autoCrop
          />
        ))

      case 'video':
        return (
          <Flex gap={12} wrap>
            {urlArray.map((url, index) => (
              <VideoView
                key={index}
                videoUrl={url}
                width={width}
                height={height}
                showPlayIcon={showPreview}
                style={{ cursor: showPreview ? 'pointer' : 'default' }}
              />
            ))}
          </Flex>
        )

      case 'inputNumber': {
        // 找到对应的媒体类型配置
        const mediaConfig = MEDIA_TYPE_OPTIONS.find(option => option.value === media.mediaType)
        const unit = mediaConfig?.unit || ''
        const value = media.mediaUrl || '0'

        // 格式化价格显示
        const formatPrice = (price: string) => {
          const num = parseFloat(price)

          if (isNaN(num)) {
            return '未设置'
          }

          // 如果是整数，不显示小数点
          if (num % 1 === 0) {
            return num.toLocaleString()
          }

          return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
        }

        return (
          <Space size={0} split={<Divider type="vertical" />}>
            <span className="no-margin fs-normal text-primary">
              {value === '0' || !value ? '未报价' : `${formatPrice(value)}${unit ? ` ${unit}` : ''}`}
            </span>
            {!!media.description && (
              <Typography.Text>
                {media.description.includes('《') ? media.description : `《${media.description}》`}
              </Typography.Text>
            )}
          </Space>
        )
      }

      default:
        return (
          <Typography.Link onClick={() => handleDownload()}>
            <Space>
              <FilePdfOutlined />
              简历
            </Space>
          </Typography.Link>
        )
    }
  }

  return (
    <>
      {/* 媒体内容 */}
      {renderContent()}

      {/* 附加信息 */}
      {showInfo && (
        <div style={{ marginTop: 8 }}>
          <Tag color={color}>{label}</Tag>
          {media.description && (
            <Typography.Text type="secondary" style={{ fontSize: '12px', display: 'block', marginTop: 4 }}>
              {media.description}
            </Typography.Text>
          )}
        </div>
      )}

      {/* 操作按钮 */}
      {showActions && (
        <Space style={{ marginTop: 8 }}>
          {showPreview && component !== 'inputNumber' && component !== 'image' && component !== 'video' && (
            <Button type="text" size="small" icon={<EyeOutlined />} onClick={() => handlePreview()}>
              预览
            </Button>
          )}
          {component !== 'inputNumber' && (
            <Button type="text" size="small" icon={<DownloadOutlined />} onClick={() => handleDownload()}>
              下载
            </Button>
          )}
        </Space>
      )}

      {/* 预览模态框 - 仅用于非视频文件 */}
      {component !== 'video' && (
        <Modal
          title={`${label} - 预览`}
          open={previewOpen}
          onCancel={handleModalCancel}
          footer={null}
          width={800}
          centered>
          {component === 'file' && (
            <div style={{ textAlign: 'center', padding: '40px' }}>
              <Space direction="vertical" size="large">
                {renderIcon()}
                <div>
                  <Typography.Title level={4}>{media.description || '文件预览'}</Typography.Title>
                  <Typography.Text type="secondary">该文件类型不支持在线预览，请下载查看</Typography.Text>
                </div>
                <Button type="primary" icon={<DownloadOutlined />} onClick={() => handleDownload()}>
                  下载文件
                </Button>
              </Space>
            </div>
          )}
        </Modal>
      )}
    </>
  )
}

export default MediaRenderer
