# PR 制片系统

## 项目概述

PR 制片系统是一个基于 React + TypeScript + Zustand 的现代化制片管理平台，用于管理短剧制作过程中的人员、项目、财务、住宿等各个环节。

## 技术栈

### 前端框架
- **React 18.3.1** - 主框架
- **TypeScript 5.5.4** - 类型系统
- **Ant Design 5.20.3** - UI 组件库
- **Zustand 5.0.5** - 状态管理
- **React Router DOM 6.30.0** - 路由管理

### 构建工具
- **Webpack 5** - 模块打包器
- **ESBuild** - 快速编译
- **Sass** - CSS 预处理器
- **PostCSS** - CSS 后处理器

### 开发工具
- **ESLint** - 代码检查
- **Prettier** - 代码格式化
- **Stylelint** - 样式检查
- **Husky** - Git 钩子
- **Lint-staged** - 暂存区检查

### 第三方库
- **Axios** - HTTP 请求
- **Day.js** - 日期处理
- **ECharts** - 图表库
- **React Beautiful DnD** - 拖拽功能
- **COS SDK** - 腾讯云对象存储

## 环境配置

- **开发环境**：pr-dev.changdu.ltd
- **测试环境**：pr-test.changdu.ltd
- **预发环境**：pr-stage.changdu.vip
- **生产环境**：pr.changdu.vip

## 项目结构

```
src/
├── assets/          # 静态资源
├── components/      # 公共组件
├── consts/          # 常量定义
├── hooks/           # 自定义 Hooks
├── layouts/         # 布局组件
├── pages/           # 页面组件
│   ├── production/  # 制片管理
│   ├── room/        # 住宿管理
│   └── system/      # 系统管理
├── types/           # 类型定义
├── utils/           # 工具函数
└── router.tsx       # 路由配置
```

## 核心功能模块

### 1. 制片管理 (Production)
- **项目管理** - 短剧项目的创建、编辑、查看
- **人员管理** - 演员、工作人员信息管理
- **财务管理** - 预算、结算、支付管理
- **合同管理** - 合同签署、状态跟踪
- **场景管理** - 拍摄场景、计划安排

### 2. 住宿管理 (Room)
- **房间管理** - 房间信息、状态管理
- **入住管理** - 人员入住、退房管理
- **社区管理** - 住宿社区信息管理

### 3. 系统管理 (System)
- **用户管理** - 用户权限、角色管理
- **字典管理** - 系统配置、枚举管理

## 角色类型枚举

```typescript
export enum RoleType {
  Director = 1, // 导演
  Screenwriter = 2, // 编剧
  Producer = 3, // 制片人
  Photographer = 4, // 摄影
  Lighting = 5, // 灯光
  Costume = 6, // 服装
  Props = 7, // 道具
  Makeup = 8, // 化妆
  Actor = 9, // 演员
  ExecutiveProducer = 10, // 总监制
  ResponsibleProducer = 11, // 负责监制
  LineProducer = 12, // 执行制片
  ActorCoordinator = 13, // 演员统筹
  AssistantDirectorActor = 14, // 演员副导
  Coordinator = 15, // 统筹
  ExecutiveDirector = 16, // 执行导演
  DIT = 17, // DIT
  FieldProducer = 18, // 现场制片
  Driver = 19, // 司机
  ArtDirector = 20, // 美术
  CostumeAndMakeupHead = 21, // 服化负责人
  ProductionManager = 22, // 制片主任
  ExternalProducer = 23, // 外联制片
  LifeProducer = 24, // 生活制片
  SetAssistant = 25, // 场务
  ScriptSupervisor = 26, // 场记
  PhotographyAssistant = 27, // 摄影助理
  StillPhotographer = 28, // 剧照
  SoundRecordist = 29, // 收音师
  SoundAssistant = 30, // 收音助理
  LightingTechnician = 31, // 灯光师
  LightingAssistant = 32, // 灯光师助理
  Stylist = 33, // 造型师
  OnSetSupervisor = 34, // 现场主盯
  OnSetMakeup = 35, // 现场跟妆
  MakeupArtist = 36, // 改妆师
  HairAndMakeup = 37, // 梳化
  SetConstruction = 38, // 制景
  CameraEquipment = 39, // 摄影器材
  LightingEquipment = 40, // 灯光器材
  SetSupplies = 41, // 场务用品
  MartialArtsDirector = 42, // 武术指导
  Wuxing = 43, // 武行
  ArtAssistant = 44, // 美术助理
  StylistAssistant = 45, // 造型师助理
  CostumeAssistant = 46, // 服装助理
  DirectorAssistant = 47, // 导演助理
  ProducerAssistant = 48, // 制片助理
  MakeupAssistant = 49, // 化妆助理
  ActorAssistant = 50, // 演员助理
  Cleaner = 51 // 保洁人员
}
```

## 开发指南

### 安装依赖
```bash
pnpm install
```

### 启动开发服务器
```bash
pnpm dev
```

### 构建生产版本
```bash
pnpm build
```

### 代码检查
```bash
pnpm stylelint
```

## 代码规范

- 使用 TypeScript 进行类型检查
- 遵循 ESLint 和 Prettier 配置
- 组件使用函数式组件 + Hooks
- 状态管理使用 Zustand
- 样式使用 SCSS + CSS Modules
- 接口请求统一使用 utils/request.ts

## API 接口规范

- 基础 URL 根据环境自动切换
- 统一的错误处理和响应格式
- 支持请求进度显示
- 自动处理认证和权限